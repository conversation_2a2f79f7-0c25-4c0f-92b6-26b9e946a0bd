import 'package:flutter/material.dart';
import 'screens/screens.dart';
import 'models/models.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'OKitchen - Gợi ý món ăn',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.orange),
        useMaterial3: true,
      ),
      home: const IngredientSelectionScreen(),
      routes: {
        '/ingredient-selection': (context) => const IngredientSelectionScreen(),
        '/recommendations': (context) => const RecipeRecommendationsScreen(),
      },
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/recipe-detail':
            final args = settings.arguments as Map<String, dynamic>;
            return MaterialPageRoute(
              builder: (context) => RecipeDetailScreen(
                recipe: args['recipe'] as Recipe,
                ingredients: args['ingredients'] as List<IngredientWithQuantity>?,
              ),
            );
          default:
            return null;
        }
      },
    );
  }
}


