import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../widgets/recipe_ingredients_section.dart';
import '../widgets/recipe_steps_section.dart';
import '../widgets/recipe_info_card.dart';

class RecipeDetailScreen extends StatefulWidget {
  final Recipe recipe;
  final List<IngredientWithQuantity>? ingredients;

  const RecipeDetailScreen({
    super.key,
    required this.recipe,
    this.ingredients,
  });

  @override
  State<RecipeDetailScreen> createState() => _RecipeDetailScreenState();
}

class _RecipeDetailScreenState extends State<RecipeDetailScreen> {
  final RecipeService _recipeService = RecipeService();
  final RecipeIngredientService _recipeIngredientService = RecipeIngredientService();
  final UserPreferenceService _userPreferenceService = UserPreferenceService();
  
  List<IngredientWithQuantity> _ingredients = [];
  bool _isLoading = false;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.recipe.isFavorite;
    if (widget.ingredients != null) {
      _ingredients = widget.ingredients!;
    } else {
      _loadIngredients();
    }
  }

  Future<void> _loadIngredients() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userSelection = await _userPreferenceService.getSelectedIngredients();
      final ingredients = await _recipeIngredientService.getIngredientsWithQuantity(
        widget.recipe.id!,
        userSelection.selectedIngredientIds,
      );

      setState(() {
        _ingredients = ingredients;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tải nguyên liệu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleFavorite() async {
    try {
      await _recipeService.toggleFavorite(widget.recipe.id!);
      setState(() {
        _isFavorite = !_isFavorite;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isFavorite 
                ? 'Đã thêm vào yêu thích' 
                : 'Đã xóa khỏi yêu thích',
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _addToMealPlan() {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    ).then((selectedDate) async {
      if (selectedDate != null) {
        try {
          final dateString = selectedDate.toIso8601String().split('T')[0];
          await _userPreferenceService.addRecipeToMealPlan(
            dateString,
            widget.recipe.id!,
          );
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Đã thêm vào lịch ngày ${selectedDate.day}/${selectedDate.month}'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Lỗi khi thêm vào lịch: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    });
  }

  void _shareRecipe() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tính năng chia sẻ sẽ được cập nhật sau'),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar with image
          _buildSliverAppBar(),
          
          // Content
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Recipe title and basic info
                _buildRecipeHeader(),
                
                // Recipe info cards
                RecipeInfoCard(recipe: widget.recipe),
                
                const SizedBox(height: 16),
                
                // Ingredients section
                if (_isLoading)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else
                  RecipeIngredientsSection(ingredients: _ingredients),
                
                const SizedBox(height: 16),
                
                // Steps section
                RecipeStepsSection(steps: widget.recipe.steps),
                
                const SizedBox(height: 32),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 250,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Recipe image
            widget.recipe.imageUrl != null
                ? Image.network(
                    widget.recipe.imageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Theme.of(context).colorScheme.surfaceVariant,
                        child: const Icon(
                          Icons.restaurant,
                          size: 64,
                          color: Colors.grey,
                        ),
                      );
                    },
                  )
                : Container(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    child: const Icon(
                      Icons.restaurant,
                      size: 64,
                      color: Colors.grey,
                    ),
                  ),
            
            // Gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.3),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          onPressed: _toggleFavorite,
          icon: Icon(
            _isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _isFavorite ? Colors.red : Colors.white,
          ),
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'meal_plan':
                _addToMealPlan();
                break;
              case 'share':
                _shareRecipe();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'meal_plan',
              child: Row(
                children: [
                  Icon(Icons.calendar_today),
                  SizedBox(width: 8),
                  Text('Thêm vào lịch'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('Chia sẻ'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecipeHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.recipe.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          if (widget.recipe.description != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.recipe.description!,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
          
          if (widget.recipe.category != null) ...[
            const SizedBox(height: 8),
            Chip(
              label: Text(widget.recipe.category!),
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              labelStyle: TextStyle(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
