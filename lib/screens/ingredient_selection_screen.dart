import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../widgets/ingredient_category_section.dart';
import '../widgets/ingredient_search_bar.dart';

class IngredientSelectionScreen extends StatefulWidget {
  const IngredientSelectionScreen({super.key});

  @override
  State<IngredientSelectionScreen> createState() => _IngredientSelectionScreenState();
}

class _IngredientSelectionScreenState extends State<IngredientSelectionScreen> {
  final IngredientService _ingredientService = IngredientService();
  final UserPreferenceService _userPreferenceService = UserPreferenceService();
  
  Map<String, List<Ingredient>> _ingredientsByCategory = {};
  UserIngredientSelection _userSelection = UserIngredientSelection.empty();
  bool _isLoading = true;
  String _searchQuery = '';
  List<Ingredient> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final [ingredientsByCategory, userSelection] = await Future.wait([
        _ingredientService.getIngredientsGroupedByCategory(),
        _userPreferenceService.getSelectedIngredients(),
      ]);

      setState(() {
        _ingredientsByCategory = ingredientsByCategory as Map<String, List<Ingredient>>;
        _userSelection = userSelection as UserIngredientSelection;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tải dữ liệu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _onIngredientToggle(int ingredientId) async {
    try {
      await _userPreferenceService.toggleSelectedIngredient(ingredientId);
      final updatedSelection = await _userPreferenceService.getSelectedIngredients();
      
      setState(() {
        _userSelection = updatedSelection;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi cập nhật: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _onSearch(String query) async {
    setState(() {
      _searchQuery = query;
      _isSearching = query.isNotEmpty;
    });

    if (query.isNotEmpty) {
      try {
        final results = await _ingredientService.searchIngredients(query);
        setState(() {
          _searchResults = results;
        });
      } catch (e) {
        setState(() {
          _searchResults = [];
        });
      }
    }
  }

  void _clearSelection() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa tất cả'),
        content: const Text('Bạn có chắc muốn xóa tất cả nguyên liệu đã chọn?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _userPreferenceService.clearSelectedIngredients();
              final updatedSelection = await _userPreferenceService.getSelectedIngredients();
              setState(() {
                _userSelection = updatedSelection;
              });
            },
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  void _navigateToRecommendations() {
    if (_userSelection.selectedIngredientIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn ít nhất một nguyên liệu'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.of(context).pushNamed('/recommendations');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chọn nguyên liệu'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_userSelection.selectedIngredientIds.isNotEmpty)
            IconButton(
              onPressed: _clearSelection,
              icon: const Icon(Icons.clear_all),
              tooltip: 'Xóa tất cả',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Search bar
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: IngredientSearchBar(
                    onSearch: _onSearch,
                    selectedCount: _userSelection.selectedIngredientIds.length,
                  ),
                ),
                
                // Content
                Expanded(
                  child: _isSearching
                      ? _buildSearchResults()
                      : _buildCategoryList(),
                ),
              ],
            ),
      bottomNavigationBar: _userSelection.selectedIngredientIds.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: ElevatedButton.icon(
                  onPressed: _navigateToRecommendations,
                  icon: const Icon(Icons.restaurant_menu),
                  label: Text(
                    'Tìm món ăn (${_userSelection.selectedIngredientIds.length} nguyên liệu)',
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Không tìm thấy nguyên liệu nào',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final ingredient = _searchResults[index];
        final isSelected = _userSelection.isSelected(ingredient.id!);

        return Card(
          child: CheckboxListTile(
            title: Text(ingredient.name),
            subtitle: Text(ingredient.category),
            value: isSelected,
            onChanged: (_) => _onIngredientToggle(ingredient.id!),
            secondary: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              child: Text(
                ingredient.name[0].toUpperCase(),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryList() {
    if (_ingredientsByCategory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Không có nguyên liệu nào',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemCount: _ingredientsByCategory.length,
      itemBuilder: (context, index) {
        final category = _ingredientsByCategory.keys.elementAt(index);
        final ingredients = _ingredientsByCategory[category]!;

        return IngredientCategorySection(
          category: category,
          ingredients: ingredients,
          userSelection: _userSelection,
          onIngredientToggle: _onIngredientToggle,
        );
      },
    );
  }
}
