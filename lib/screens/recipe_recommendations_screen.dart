import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../widgets/recipe_match_card.dart';
import '../widgets/filter_bottom_sheet.dart';

class RecipeRecommendationsScreen extends StatefulWidget {
  const RecipeRecommendationsScreen({super.key});

  @override
  State<RecipeRecommendationsScreen> createState() => _RecipeRecommendationsScreenState();
}

class _RecipeRecommendationsScreenState extends State<RecipeRecommendationsScreen> {
  final RecipeRecommendationService _recommendationService = RecipeRecommendationService();
  final UserPreferenceService _userPreferenceService = UserPreferenceService();
  
  List<RecipeMatch> _recommendations = [];
  List<RecipeMatch> _filteredRecommendations = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // all, perfect, good, partial
  DifficultyLevel? _difficultyFilter;
  int? _maxCookingTime;
  String? _categoryFilter;

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final recommendations = await _recommendationService.getRecommendations(
        maxResults: 50,
        minMatchPercentage: 20.0,
      );

      setState(() {
        _recommendations = recommendations;
        _filteredRecommendations = recommendations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tải gợi ý: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _applyFilters() {
    List<RecipeMatch> filtered = List.from(_recommendations);

    // Apply match status filter
    switch (_selectedFilter) {
      case 'perfect':
        filtered = filtered.where((match) => 
            match.matchStatus == RecipeMatchStatus.perfect).toList();
        break;
      case 'good':
        filtered = filtered.where((match) => 
            match.matchStatus == RecipeMatchStatus.good).toList();
        break;
      case 'partial':
        filtered = filtered.where((match) => 
            match.matchStatus == RecipeMatchStatus.partial).toList();
        break;
      case 'all':
      default:
        // No filter
        break;
    }

    // Apply difficulty filter
    if (_difficultyFilter != null) {
      filtered = filtered.where((match) => 
          match.recipe.difficulty == _difficultyFilter).toList();
    }

    // Apply cooking time filter
    if (_maxCookingTime != null) {
      filtered = filtered.where((match) => 
          match.recipe.cookingTimeMinutes <= _maxCookingTime!).toList();
    }

    // Apply category filter
    if (_categoryFilter != null && _categoryFilter!.isNotEmpty) {
      filtered = filtered.where((match) => 
          match.recipe.category == _categoryFilter).toList();
    }

    setState(() {
      _filteredRecommendations = filtered;
    });
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => FilterBottomSheet(
        selectedFilter: _selectedFilter,
        difficultyFilter: _difficultyFilter,
        maxCookingTime: _maxCookingTime,
        categoryFilter: _categoryFilter,
        onFiltersChanged: (filter, difficulty, cookingTime, category) {
          setState(() {
            _selectedFilter = filter;
            _difficultyFilter = difficulty;
            _maxCookingTime = cookingTime;
            _categoryFilter = category;
          });
          _applyFilters();
        },
      ),
    );
  }

  void _navigateToRecipeDetail(RecipeMatch match) {
    Navigator.of(context).pushNamed(
      '/recipe-detail',
      arguments: {
        'recipe': match.recipe,
        'ingredients': match.ingredients,
      },
    );
  }

  void _navigateToIngredientSelection() {
    Navigator.of(context).pushReplacementNamed('/ingredient-selection');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gợi ý món ăn'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: _showFilterBottomSheet,
            icon: Badge(
              isLabelVisible: _hasActiveFilters(),
              child: const Icon(Icons.filter_list),
            ),
            tooltip: 'Lọc kết quả',
          ),
          IconButton(
            onPressed: _loadRecommendations,
            icon: const Icon(Icons.refresh),
            tooltip: 'Làm mới',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToIngredientSelection,
        tooltip: 'Chọn nguyên liệu',
        child: const Icon(Icons.edit),
      ),
    );
  }

  Widget _buildContent() {
    if (_recommendations.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Summary header
        _buildSummaryHeader(),
        
        // Recommendations list
        Expanded(
          child: _filteredRecommendations.isEmpty
              ? _buildNoResultsState()
              : _buildRecommendationsList(),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.restaurant_menu_outlined,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Chưa có gợi ý món ăn',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          const Text(
            'Hãy chọn nguyên liệu để nhận gợi ý',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _navigateToIngredientSelection,
            icon: const Icon(Icons.add),
            label: const Text('Chọn nguyên liệu'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Không tìm thấy món ăn phù hợp',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          const Text(
            'Thử thay đổi bộ lọc hoặc chọn thêm nguyên liệu',
            style: TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedFilter = 'all';
                _difficultyFilter = null;
                _maxCookingTime = null;
                _categoryFilter = null;
              });
              _applyFilters();
            },
            icon: const Icon(Icons.clear_all),
            label: const Text('Xóa bộ lọc'),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Tìm thấy ${_filteredRecommendations.length} món ăn phù hợp',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (_hasActiveFilters())
            Chip(
              label: const Text('Đã lọc'),
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              labelStyle: TextStyle(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredRecommendations.length,
      itemBuilder: (context, index) {
        final match = _filteredRecommendations[index];
        return RecipeMatchCard(
          match: match,
          onTap: () => _navigateToRecipeDetail(match),
        );
      },
    );
  }

  bool _hasActiveFilters() {
    return _selectedFilter != 'all' ||
           _difficultyFilter != null ||
           _maxCookingTime != null ||
           (_categoryFilter != null && _categoryFilter!.isNotEmpty);
  }
}
