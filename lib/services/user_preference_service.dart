import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class UserPreferenceService {
  static const String _selectedIngredientsKey = 'selected_ingredients';
  static const String _favoriteRecipesKey = 'favorite_recipes';
  static const String _mealPlanKey = 'meal_plan';
  static const String _appSettingsKey = 'app_settings';

  // Get user's selected ingredients
  Future<UserIngredientSelection> getSelectedIngredients() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_selectedIngredientsKey);
    
    if (jsonString != null) {
      try {
        final Map<String, dynamic> json = jsonDecode(jsonString);
        return UserIngredientSelection.fromMap(json);
      } catch (e) {
        // If parsing fails, return empty selection
        return UserIngredientSelection.empty();
      }
    }
    
    return UserIngredientSelection.empty();
  }

  // Save user's selected ingredients
  Future<bool> saveSelectedIngredients(UserIngredientSelection selection) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(selection.toMap());
    return await prefs.setString(_selectedIngredientsKey, jsonString);
  }

  // Add ingredient to selection
  Future<bool> addSelectedIngredient(int ingredientId) async {
    final currentSelection = await getSelectedIngredients();
    final newSelection = currentSelection.addIngredient(ingredientId);
    return await saveSelectedIngredients(newSelection);
  }

  // Remove ingredient from selection
  Future<bool> removeSelectedIngredient(int ingredientId) async {
    final currentSelection = await getSelectedIngredients();
    final newSelection = currentSelection.removeIngredient(ingredientId);
    return await saveSelectedIngredients(newSelection);
  }

  // Toggle ingredient selection
  Future<bool> toggleSelectedIngredient(int ingredientId) async {
    final currentSelection = await getSelectedIngredients();
    final newSelection = currentSelection.toggleIngredient(ingredientId);
    return await saveSelectedIngredients(newSelection);
  }

  // Clear all selected ingredients
  Future<bool> clearSelectedIngredients() async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.remove(_selectedIngredientsKey);
  }

  // Get favorite recipe IDs
  Future<Set<int>> getFavoriteRecipeIds() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? favoriteStrings = prefs.getStringList(_favoriteRecipesKey);
    
    if (favoriteStrings != null) {
      return favoriteStrings.map((s) => int.parse(s)).toSet();
    }
    
    return <int>{};
  }

  // Save favorite recipe IDs
  Future<bool> saveFavoriteRecipeIds(Set<int> favoriteIds) async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> favoriteStrings = favoriteIds.map((id) => id.toString()).toList();
    return await prefs.setStringList(_favoriteRecipesKey, favoriteStrings);
  }

  // Add recipe to favorites
  Future<bool> addFavoriteRecipe(int recipeId) async {
    final currentFavorites = await getFavoriteRecipeIds();
    currentFavorites.add(recipeId);
    return await saveFavoriteRecipeIds(currentFavorites);
  }

  // Remove recipe from favorites
  Future<bool> removeFavoriteRecipe(int recipeId) async {
    final currentFavorites = await getFavoriteRecipeIds();
    currentFavorites.remove(recipeId);
    return await saveFavoriteRecipeIds(currentFavorites);
  }

  // Toggle recipe favorite status
  Future<bool> toggleFavoriteRecipe(int recipeId) async {
    final currentFavorites = await getFavoriteRecipeIds();
    
    if (currentFavorites.contains(recipeId)) {
      currentFavorites.remove(recipeId);
    } else {
      currentFavorites.add(recipeId);
    }
    
    return await saveFavoriteRecipeIds(currentFavorites);
  }

  // Check if recipe is favorite
  Future<bool> isRecipeFavorite(int recipeId) async {
    final favorites = await getFavoriteRecipeIds();
    return favorites.contains(recipeId);
  }

  // Meal plan methods
  Future<Map<String, List<int>>> getMealPlan() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_mealPlanKey);
    
    if (jsonString != null) {
      try {
        final Map<String, dynamic> json = jsonDecode(jsonString);
        final Map<String, List<int>> mealPlan = {};
        
        json.forEach((key, value) {
          mealPlan[key] = List<int>.from(value);
        });
        
        return mealPlan;
      } catch (e) {
        return {};
      }
    }
    
    return {};
  }

  Future<bool> saveMealPlan(Map<String, List<int>> mealPlan) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(mealPlan);
    return await prefs.setString(_mealPlanKey, jsonString);
  }

  Future<bool> addRecipeToMealPlan(String date, int recipeId) async {
    final currentPlan = await getMealPlan();
    
    if (!currentPlan.containsKey(date)) {
      currentPlan[date] = [];
    }
    
    if (!currentPlan[date]!.contains(recipeId)) {
      currentPlan[date]!.add(recipeId);
    }
    
    return await saveMealPlan(currentPlan);
  }

  Future<bool> removeRecipeFromMealPlan(String date, int recipeId) async {
    final currentPlan = await getMealPlan();
    
    if (currentPlan.containsKey(date)) {
      currentPlan[date]!.remove(recipeId);
      
      // Remove empty dates
      if (currentPlan[date]!.isEmpty) {
        currentPlan.remove(date);
      }
    }
    
    return await saveMealPlan(currentPlan);
  }

  Future<List<int>> getRecipesForDate(String date) async {
    final mealPlan = await getMealPlan();
    return mealPlan[date] ?? [];
  }

  // App settings
  Future<Map<String, dynamic>> getAppSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_appSettingsKey);
    
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString);
      } catch (e) {
        return _getDefaultSettings();
      }
    }
    
    return _getDefaultSettings();
  }

  Future<bool> saveAppSettings(Map<String, dynamic> settings) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(settings);
    return await prefs.setString(_appSettingsKey, jsonString);
  }

  Map<String, dynamic> _getDefaultSettings() {
    return {
      'theme_mode': 'system', // 'light', 'dark', 'system'
      'language': 'vi',
      'show_optional_ingredients': true,
      'default_servings': 2,
      'max_cooking_time': 120, // minutes
      'preferred_difficulty': 'all', // 'easy', 'medium', 'hard', 'all'
    };
  }

  // Clear all user data
  Future<bool> clearAllUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final futures = [
      prefs.remove(_selectedIngredientsKey),
      prefs.remove(_favoriteRecipesKey),
      prefs.remove(_mealPlanKey),
      prefs.remove(_appSettingsKey),
    ];
    
    final results = await Future.wait(futures);
    return results.every((result) => result);
  }
}
