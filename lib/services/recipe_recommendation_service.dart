import '../models/models.dart';
import 'services.dart';

class RecipeRecommendationService {
  final RecipeService _recipeService = RecipeService();
  final RecipeIngredientService _recipeIngredientService = RecipeIngredientService();
  final IngredientService _ingredientService = IngredientService();
  final UserPreferenceService _userPreferenceService = UserPreferenceService();

  // Get recipe recommendations based on available ingredients
  Future<List<RecipeMatch>> getRecommendations({
    Set<int>? availableIngredientIds,
    int? maxResults,
    double? minMatchPercentage,
    DifficultyLevel? maxDifficulty,
    int? maxCookingTime,
    String? category,
    bool includeOptionalIngredients = true,
  }) async {
    // Get available ingredients from user selection if not provided
    availableIngredientIds ??= await _getUserSelectedIngredients();
    
    if (availableIngredientIds.isEmpty) {
      return [];
    }

    // Get all recipes that use at least one of the available ingredients
    final candidateRecipeIds = await _recipeIngredientService
        .getRecipeIdsUsingIngredients(availableIngredientIds.toList());

    if (candidateRecipeIds.isEmpty) {
      return [];
    }

    // Get recipe details
    List<Recipe> candidateRecipes = await _recipeService
        .getRecipesByIds(candidateRecipeIds);

    // Apply filters
    candidateRecipes = _applyFilters(
      candidateRecipes,
      maxDifficulty: maxDifficulty,
      maxCookingTime: maxCookingTime,
      category: category,
    );

    // Calculate matches for remaining recipes
    final List<RecipeMatch> matches = [];
    
    for (final recipe in candidateRecipes) {
      final match = await _calculateRecipeMatch(
        recipe,
        availableIngredientIds,
        includeOptionalIngredients: includeOptionalIngredients,
      );
      
      // Apply minimum match percentage filter
      if (minMatchPercentage == null || match.matchPercentage >= minMatchPercentage) {
        matches.add(match);
      }
    }

    // Sort by match percentage (descending), then by cooking time (ascending)
    matches.sort((a, b) {
      final matchComparison = b.matchPercentage.compareTo(a.matchPercentage);
      if (matchComparison != 0) return matchComparison;
      
      return a.recipe.cookingTimeMinutes.compareTo(b.recipe.cookingTimeMinutes);
    });

    // Apply max results limit
    if (maxResults != null && matches.length > maxResults) {
      return matches.take(maxResults).toList();
    }

    return matches;
  }

  // Get quick recommendations (perfect or near-perfect matches)
  Future<List<RecipeMatch>> getQuickRecommendations({
    Set<int>? availableIngredientIds,
    int maxResults = 10,
  }) async {
    return await getRecommendations(
      availableIngredientIds: availableIngredientIds,
      maxResults: maxResults,
      minMatchPercentage: 80.0,
      maxCookingTime: 30,
    );
  }

  // Get recommendations for specific meal type
  Future<List<RecipeMatch>> getMealRecommendations(
    String mealType, {
    Set<int>? availableIngredientIds,
    int maxResults = 15,
  }) async {
    return await getRecommendations(
      availableIngredientIds: availableIngredientIds,
      maxResults: maxResults,
      category: mealType,
      minMatchPercentage: 60.0,
    );
  }

  // Get recommendations by difficulty level
  Future<List<RecipeMatch>> getRecommendationsByDifficulty(
    DifficultyLevel difficulty, {
    Set<int>? availableIngredientIds,
    int maxResults = 20,
  }) async {
    return await getRecommendations(
      availableIngredientIds: availableIngredientIds,
      maxResults: maxResults,
      maxDifficulty: difficulty,
      minMatchPercentage: 50.0,
    );
  }

  // Calculate match for a specific recipe
  Future<RecipeMatch> _calculateRecipeMatch(
    Recipe recipe,
    Set<int> availableIngredientIds, {
    bool includeOptionalIngredients = true,
  }) async {
    final ingredients = await _recipeIngredientService
        .getIngredientsWithQuantity(recipe.id!, availableIngredientIds);

    int totalRequired = 0;
    int availableRequired = 0;
    int totalOptional = 0;
    int availableOptional = 0;

    for (final ingredient in ingredients) {
      if (ingredient.isOptional) {
        totalOptional++;
        if (ingredient.isAvailable) availableOptional++;
      } else {
        totalRequired++;
        if (ingredient.isAvailable) availableRequired++;
      }
    }

    // Calculate match percentage
    double matchPercentage = 0.0;
    
    if (totalRequired > 0) {
      // Base match on required ingredients (primary weight)
      final requiredMatch = availableRequired / totalRequired;
      
      if (includeOptionalIngredients && totalOptional > 0) {
        // Weighted average: 80% required, 20% optional
        final optionalMatch = availableOptional / totalOptional;
        matchPercentage = (requiredMatch * 0.8) + (optionalMatch * 0.2);
      } else {
        matchPercentage = requiredMatch;
      }
    }

    final totalIngredients = totalRequired + (includeOptionalIngredients ? totalOptional : 0);
    final availableIngredients = availableRequired + (includeOptionalIngredients ? availableOptional : 0);
    final missingIngredients = totalIngredients - availableIngredients;

    return RecipeMatch(
      recipe: recipe,
      ingredients: ingredients,
      matchPercentage: matchPercentage * 100,
      availableIngredientsCount: availableIngredients,
      totalIngredientsCount: totalIngredients,
      missingIngredientsCount: missingIngredients,
    );
  }

  // Apply various filters to recipes
  List<Recipe> _applyFilters(
    List<Recipe> recipes, {
    DifficultyLevel? maxDifficulty,
    int? maxCookingTime,
    String? category,
  }) {
    return recipes.where((recipe) {
      // Difficulty filter
      if (maxDifficulty != null && recipe.difficulty.value > maxDifficulty.value) {
        return false;
      }

      // Cooking time filter
      if (maxCookingTime != null && recipe.cookingTimeMinutes > maxCookingTime) {
        return false;
      }

      // Category filter
      if (category != null && recipe.category != category) {
        return false;
      }

      return true;
    }).toList();
  }

  // Get user's selected ingredients
  Future<Set<int>> _getUserSelectedIngredients() async {
    final selection = await _userPreferenceService.getSelectedIngredients();
    return selection.selectedIngredientIds;
  }

  // Get similar recipes based on ingredients
  Future<List<RecipeMatch>> getSimilarRecipes(
    int recipeId, {
    int maxResults = 10,
    double minSimilarity = 0.3,
  }) async {
    final targetRecipe = await _recipeService.getRecipeById(recipeId);
    if (targetRecipe == null) return [];

    // Get ingredients of the target recipe
    final targetIngredients = await _recipeIngredientService.getRecipeIngredients(recipeId);
    final targetIngredientIds = targetIngredients.map((ri) => ri.ingredientId).toSet();

    // Get all other recipes
    final allRecipes = await _recipeService.getAllRecipes();
    final otherRecipes = allRecipes.where((r) => r.id != recipeId).toList();

    final List<RecipeMatch> similarRecipes = [];

    for (final recipe in otherRecipes) {
      final recipeIngredients = await _recipeIngredientService.getRecipeIngredients(recipe.id!);
      final recipeIngredientIds = recipeIngredients.map((ri) => ri.ingredientId).toSet();

      // Calculate Jaccard similarity
      final intersection = targetIngredientIds.intersection(recipeIngredientIds);
      final union = targetIngredientIds.union(recipeIngredientIds);
      
      if (union.isEmpty) continue;
      
      final similarity = intersection.length / union.length;
      
      if (similarity >= minSimilarity) {
        final match = await _calculateRecipeMatch(recipe, targetIngredientIds);
        similarRecipes.add(match);
      }
    }

    // Sort by similarity (match percentage)
    similarRecipes.sort((a, b) => b.matchPercentage.compareTo(a.matchPercentage));

    return similarRecipes.take(maxResults).toList();
  }

  // Get trending recipes (most used ingredients)
  Future<List<RecipeMatch>> getTrendingRecipes({
    Set<int>? availableIngredientIds,
    int maxResults = 15,
  }) async {
    availableIngredientIds ??= await _getUserSelectedIngredients();
    
    if (availableIngredientIds.isEmpty) {
      // If no ingredients selected, return popular recipes
      final allRecipes = await _recipeService.getAllRecipes();
      final matches = <RecipeMatch>[];
      
      for (final recipe in allRecipes.take(maxResults)) {
        final match = await _calculateRecipeMatch(recipe, {});
        matches.add(match);
      }
      
      return matches;
    }

    // Get ingredient usage statistics
    final ingredientUsage = await _recipeIngredientService.getIngredientUsageCount();
    
    // Score recipes based on popularity of their ingredients
    final recommendations = await getRecommendations(
      availableIngredientIds: availableIngredientIds,
      maxResults: maxResults * 2, // Get more to filter
      minMatchPercentage: 30.0,
    );

    // Add popularity score
    for (final match in recommendations) {
      double popularityScore = 0.0;
      int ingredientCount = 0;
      
      for (final ingredient in match.ingredients) {
        final usage = ingredientUsage[ingredient.ingredientId] ?? 0;
        popularityScore += usage;
        ingredientCount++;
      }
      
      if (ingredientCount > 0) {
        popularityScore /= ingredientCount; // Average popularity
      }
      
      // Combine match percentage with popularity (70% match, 30% popularity)
      final combinedScore = (match.matchPercentage * 0.7) + (popularityScore * 0.3);
      // Store combined score in a custom property if needed
    }

    return recommendations.take(maxResults).toList();
  }
}
