import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class IngredientService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Get all ingredients
  Future<List<Ingredient>> getAllIngredients() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ingredients',
      orderBy: 'category, name',
    );

    return List.generate(maps.length, (i) {
      return Ingredient.fromMap(maps[i]);
    });
  }

  // Get ingredients by category
  Future<List<Ingredient>> getIngredientsByCategory(String category) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ingredients',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'name',
    );

    return List.generate(maps.length, (i) {
      return Ingredient.fromMap(maps[i]);
    });
  }

  // Get ingredients grouped by category
  Future<Map<String, List<Ingredient>>> getIngredientsGroupedByCategory() async {
    final ingredients = await getAllIngredients();
    final Map<String, List<Ingredient>> grouped = {};

    for (final ingredient in ingredients) {
      if (!grouped.containsKey(ingredient.category)) {
        grouped[ingredient.category] = [];
      }
      grouped[ingredient.category]!.add(ingredient);
    }

    return grouped;
  }

  // Get ingredient by ID
  Future<Ingredient?> getIngredientById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ingredients',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Ingredient.fromMap(maps.first);
    }
    return null;
  }

  // Get ingredients by IDs
  Future<List<Ingredient>> getIngredientsByIds(List<int> ids) async {
    if (ids.isEmpty) return [];

    final db = await _databaseHelper.database;
    final placeholders = List.filled(ids.length, '?').join(',');
    final List<Map<String, dynamic>> maps = await db.query(
      'ingredients',
      where: 'id IN ($placeholders)',
      whereArgs: ids,
      orderBy: 'category, name',
    );

    return List.generate(maps.length, (i) {
      return Ingredient.fromMap(maps[i]);
    });
  }

  // Search ingredients by name
  Future<List<Ingredient>> searchIngredients(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ingredients',
      where: 'name LIKE ?',
      whereArgs: ['%$query%'],
      orderBy: 'name',
    );

    return List.generate(maps.length, (i) {
      return Ingredient.fromMap(maps[i]);
    });
  }

  // Insert ingredient
  Future<int> insertIngredient(Ingredient ingredient) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      'ingredients',
      ingredient.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Update ingredient
  Future<int> updateIngredient(Ingredient ingredient) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'ingredients',
      ingredient.toMap(),
      where: 'id = ?',
      whereArgs: [ingredient.id],
    );
  }

  // Delete ingredient
  Future<int> deleteIngredient(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'ingredients',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get all unique categories
  Future<List<String>> getAllCategories() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ingredients',
      columns: ['category'],
      distinct: true,
      orderBy: 'category',
    );

    return maps.map((map) => map['category'] as String).toList();
  }

  // Get ingredient count by category
  Future<Map<String, int>> getIngredientCountByCategory() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT category, COUNT(*) as count
      FROM ingredients
      GROUP BY category
      ORDER BY category
    ''');

    final Map<String, int> counts = {};
    for (final map in maps) {
      counts[map['category'] as String] = map['count'] as int;
    }

    return counts;
  }

  // Batch insert ingredients
  Future<void> insertIngredients(List<Ingredient> ingredients) async {
    final db = await _databaseHelper.database;
    final batch = db.batch();

    for (final ingredient in ingredients) {
      batch.insert(
        'ingredients',
        ingredient.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit(noResult: true);
  }
}
