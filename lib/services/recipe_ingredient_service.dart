import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class RecipeIngredientService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Get all recipe ingredients for a recipe
  Future<List<RecipeIngredient>> getRecipeIngredients(int recipeId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipe_ingredients',
      where: 'recipe_id = ?',
      whereArgs: [recipeId],
      orderBy: 'id',
    );

    return List.generate(maps.length, (i) {
      return RecipeIngredient.fromMap(maps[i]);
    });
  }

  // Get ingredients with quantities for a recipe
  Future<List<IngredientWithQuantity>> getIngredientsWithQuantity(
    int recipeId,
    Set<int> availableIngredientIds,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        ri.ingredient_id,
        ri.quantity,
        ri.unit,
        ri.is_optional,
        i.name,
        i.category,
        i.image_url
      FROM recipe_ingredients ri
      JOIN ingredients i ON ri.ingredient_id = i.id
      WHERE ri.recipe_id = ?
      ORDER BY ri.is_optional, i.name
    ''', [recipeId]);

    return List.generate(maps.length, (i) {
      final map = maps[i];
      final ingredientId = map['ingredient_id'] as int;
      final isAvailable = availableIngredientIds.contains(ingredientId);

      return IngredientWithQuantity(
        ingredientId: ingredientId,
        name: map['name'] as String,
        category: map['category'] as String,
        quantity: map['quantity'] as String,
        unit: map['unit'] as String?,
        isOptional: (map['is_optional'] as int) == 1,
        isAvailable: isAvailable,
        imageUrl: map['image_url'] as String?,
      );
    });
  }

  // Get recipes that use a specific ingredient
  Future<List<int>> getRecipeIdsUsingIngredient(int ingredientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipe_ingredients',
      columns: ['recipe_id'],
      where: 'ingredient_id = ?',
      whereArgs: [ingredientId],
      distinct: true,
    );

    return maps.map((map) => map['recipe_id'] as int).toList();
  }

  // Get recipes that use any of the specified ingredients
  Future<List<int>> getRecipeIdsUsingIngredients(List<int> ingredientIds) async {
    if (ingredientIds.isEmpty) return [];

    final db = await _databaseHelper.database;
    final placeholders = List.filled(ingredientIds.length, '?').join(',');
    final List<Map<String, dynamic>> maps = await db.query(
      'recipe_ingredients',
      columns: ['recipe_id'],
      where: 'ingredient_id IN ($placeholders)',
      whereArgs: ingredientIds,
      distinct: true,
    );

    return maps.map((map) => map['recipe_id'] as int).toList();
  }

  // Calculate recipe match for given available ingredients
  Future<Map<int, double>> calculateRecipeMatches(Set<int> availableIngredientIds) async {
    final db = await _databaseHelper.database;
    
    // Get all recipe ingredients with their optional status
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        recipe_id,
        ingredient_id,
        is_optional
      FROM recipe_ingredients
      ORDER BY recipe_id
    ''');

    final Map<int, List<Map<String, dynamic>>> recipeIngredients = {};
    
    // Group by recipe_id
    for (final map in maps) {
      final recipeId = map['recipe_id'] as int;
      if (!recipeIngredients.containsKey(recipeId)) {
        recipeIngredients[recipeId] = [];
      }
      recipeIngredients[recipeId]!.add(map);
    }

    final Map<int, double> matches = {};

    // Calculate match percentage for each recipe
    for (final entry in recipeIngredients.entries) {
      final recipeId = entry.key;
      final ingredients = entry.value;
      
      int totalRequired = 0;
      int availableRequired = 0;
      int totalOptional = 0;
      int availableOptional = 0;

      for (final ingredient in ingredients) {
        final ingredientId = ingredient['ingredient_id'] as int;
        final isOptional = (ingredient['is_optional'] as int) == 1;
        final isAvailable = availableIngredientIds.contains(ingredientId);

        if (isOptional) {
          totalOptional++;
          if (isAvailable) availableOptional++;
        } else {
          totalRequired++;
          if (isAvailable) availableRequired++;
        }
      }

      // Calculate match percentage
      // Required ingredients have more weight than optional ones
      double matchPercentage = 0.0;
      
      if (totalRequired > 0) {
        // Base match on required ingredients (80% weight)
        final requiredMatch = (availableRequired / totalRequired) * 0.8;
        matchPercentage += requiredMatch;
        
        // Add bonus for optional ingredients (20% weight)
        if (totalOptional > 0) {
          final optionalMatch = (availableOptional / totalOptional) * 0.2;
          matchPercentage += optionalMatch;
        } else {
          // If no optional ingredients, give full weight to required
          matchPercentage = availableRequired / totalRequired;
        }
      }

      matches[recipeId] = matchPercentage * 100; // Convert to percentage
    }

    return matches;
  }

  // Insert recipe ingredient
  Future<int> insertRecipeIngredient(RecipeIngredient recipeIngredient) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      'recipe_ingredients',
      recipeIngredient.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Update recipe ingredient
  Future<int> updateRecipeIngredient(RecipeIngredient recipeIngredient) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'recipe_ingredients',
      recipeIngredient.toMap(),
      where: 'id = ?',
      whereArgs: [recipeIngredient.id],
    );
  }

  // Delete recipe ingredient
  Future<int> deleteRecipeIngredient(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'recipe_ingredients',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Delete all recipe ingredients for a recipe
  Future<int> deleteRecipeIngredients(int recipeId) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'recipe_ingredients',
      where: 'recipe_id = ?',
      whereArgs: [recipeId],
    );
  }

  // Batch insert recipe ingredients
  Future<void> insertRecipeIngredients(List<RecipeIngredient> recipeIngredients) async {
    final db = await _databaseHelper.database;
    final batch = db.batch();

    for (final recipeIngredient in recipeIngredients) {
      batch.insert(
        'recipe_ingredients',
        recipeIngredient.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit(noResult: true);
  }

  // Get ingredient usage statistics
  Future<Map<int, int>> getIngredientUsageCount() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT ingredient_id, COUNT(*) as usage_count
      FROM recipe_ingredients
      GROUP BY ingredient_id
      ORDER BY usage_count DESC
    ''');

    final Map<int, int> usage = {};
    for (final map in maps) {
      usage[map['ingredient_id'] as int] = map['usage_count'] as int;
    }

    return usage;
  }
}
