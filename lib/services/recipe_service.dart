import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class RecipeService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Get all recipes
  Future<List<Recipe>> getAllRecipes() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      orderBy: 'title',
    );

    return List.generate(maps.length, (i) {
      return Recipe.fromMap(maps[i]);
    });
  }

  // Get recipe by ID
  Future<Recipe?> getRecipeById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Recipe.fromMap(maps.first);
    }
    return null;
  }

  // Get recipes by IDs
  Future<List<Recipe>> getRecipesByIds(List<int> ids) async {
    if (ids.isEmpty) return [];

    final db = await _databaseHelper.database;
    final placeholders = List.filled(ids.length, '?').join(',');
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'id IN ($placeholders)',
      whereArgs: ids,
      orderBy: 'title',
    );

    return List.generate(maps.length, (i) {
      return Recipe.fromMap(maps[i]);
    });
  }

  // Get favorite recipes
  Future<List<Recipe>> getFavoriteRecipes() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'is_favorite = ?',
      whereArgs: [1],
      orderBy: 'title',
    );

    return List.generate(maps.length, (i) {
      return Recipe.fromMap(maps[i]);
    });
  }

  // Search recipes by title
  Future<List<Recipe>> searchRecipes(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'title LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'title',
    );

    return List.generate(maps.length, (i) {
      return Recipe.fromMap(maps[i]);
    });
  }

  // Get recipes by category
  Future<List<Recipe>> getRecipesByCategory(String category) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'title',
    );

    return List.generate(maps.length, (i) {
      return Recipe.fromMap(maps[i]);
    });
  }

  // Get recipes by difficulty
  Future<List<Recipe>> getRecipesByDifficulty(DifficultyLevel difficulty) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'difficulty = ?',
      whereArgs: [difficulty.value],
      orderBy: 'title',
    );

    return List.generate(maps.length, (i) {
      return Recipe.fromMap(maps[i]);
    });
  }

  // Get recipes by cooking time range
  Future<List<Recipe>> getRecipesByCookingTime(int minMinutes, int maxMinutes) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'cooking_time_minutes BETWEEN ? AND ?',
      whereArgs: [minMinutes, maxMinutes],
      orderBy: 'cooking_time_minutes',
    );

    return List.generate(maps.length, (i) {
      return Recipe.fromMap(maps[i]);
    });
  }

  // Insert recipe
  Future<int> insertRecipe(Recipe recipe) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      'recipes',
      recipe.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Update recipe
  Future<int> updateRecipe(Recipe recipe) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'recipes',
      recipe.toMap(),
      where: 'id = ?',
      whereArgs: [recipe.id],
    );
  }

  // Toggle favorite status
  Future<int> toggleFavorite(int recipeId) async {
    final db = await _databaseHelper.database;
    
    // Get current favorite status
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      columns: ['is_favorite'],
      where: 'id = ?',
      whereArgs: [recipeId],
    );

    if (maps.isEmpty) return 0;

    final currentStatus = maps.first['is_favorite'] as int;
    final newStatus = currentStatus == 1 ? 0 : 1;

    return await db.update(
      'recipes',
      {'is_favorite': newStatus},
      where: 'id = ?',
      whereArgs: [recipeId],
    );
  }

  // Delete recipe
  Future<int> deleteRecipe(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'recipes',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get all unique categories
  Future<List<String>> getAllCategories() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      columns: ['category'],
      distinct: true,
      where: 'category IS NOT NULL',
      orderBy: 'category',
    );

    return maps.map((map) => map['category'] as String).toList();
  }

  // Get recipe count by category
  Future<Map<String, int>> getRecipeCountByCategory() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT category, COUNT(*) as count
      FROM recipes
      WHERE category IS NOT NULL
      GROUP BY category
      ORDER BY category
    ''');

    final Map<String, int> counts = {};
    for (final map in maps) {
      counts[map['category'] as String] = map['count'] as int;
    }

    return counts;
  }

  // Batch insert recipes
  Future<void> insertRecipes(List<Recipe> recipes) async {
    final db = await _databaseHelper.database;
    final batch = db.batch();

    for (final recipe in recipes) {
      batch.insert(
        'recipes',
        recipe.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit(noResult: true);
  }
}
