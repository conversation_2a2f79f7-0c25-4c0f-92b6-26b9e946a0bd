import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../data/sample_data.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'okitchen.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create ingredients table
    await db.execute('''
      CREATE TABLE ingredients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        category TEXT NOT NULL,
        description TEXT,
        image_url TEXT
      )
    ''');

    // Create recipes table
    await db.execute('''
      CREATE TABLE recipes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        image_url TEXT,
        steps TEXT NOT NULL,
        cooking_time_minutes INTEGER NOT NULL,
        difficulty INTEGER NOT NULL,
        servings INTEGER NOT NULL,
        category TEXT,
        created_at TEXT,
        is_favorite INTEGER DEFAULT 0
      )
    ''');

    // Create recipe_ingredients table (many-to-many relationship)
    await db.execute('''
      CREATE TABLE recipe_ingredients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recipe_id INTEGER NOT NULL,
        ingredient_id INTEGER NOT NULL,
        quantity TEXT NOT NULL,
        unit TEXT,
        is_optional INTEGER DEFAULT 0,
        FOREIGN KEY (recipe_id) REFERENCES recipes (id) ON DELETE CASCADE,
        FOREIGN KEY (ingredient_id) REFERENCES ingredients (id) ON DELETE CASCADE,
        UNIQUE(recipe_id, ingredient_id)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_ingredients_category ON ingredients(category)');
    await db.execute('CREATE INDEX idx_recipes_category ON recipes(category)');
    await db.execute('CREATE INDEX idx_recipes_favorite ON recipes(is_favorite)');
    await db.execute('CREATE INDEX idx_recipe_ingredients_recipe ON recipe_ingredients(recipe_id)');
    await db.execute('CREATE INDEX idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)');

    // Insert sample data
    await _insertSampleData(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  Future<void> _insertSampleData(Database db) async {
    // Get sample data
    final sampleIngredients = SampleData.ingredients;
    final sampleRecipes = SampleData.recipes;
    final sampleRecipeIngredients = SampleData.getRecipeIngredients();

    // Insert ingredients
    for (final ingredient in sampleIngredients) {
      await db.insert('ingredients', ingredient);
    }

    // Insert recipes with current timestamp
    for (final recipe in sampleRecipes) {
      final recipeWithTimestamp = Map<String, dynamic>.from(recipe);
      recipeWithTimestamp['created_at'] = DateTime.now().toIso8601String();
      await db.insert('recipes', recipeWithTimestamp);
    }

    // Insert recipe ingredients with proper ingredient_id mapping
    for (final recipeIngredient in sampleRecipeIngredients) {
      // Find ingredient_id by name
      final ingredientName = recipeIngredient['ingredient_name'];
      final ingredientResult = await db.query(
        'ingredients',
        columns: ['id'],
        where: 'name = ?',
        whereArgs: [ingredientName],
      );

      if (ingredientResult.isNotEmpty) {
        final ingredientId = ingredientResult.first['id'] as int;
        await db.insert('recipe_ingredients', {
          'recipe_id': recipeIngredient['recipe_id'],
          'ingredient_id': ingredientId,
          'quantity': recipeIngredient['quantity'],
          'unit': recipeIngredient['unit'],
          'is_optional': recipeIngredient['is_optional'] ?? 0,
        });
      }
    }
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  // Reset database (for testing purposes)
  Future<void> resetDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'okitchen.db');
    
    await close();
    await deleteDatabase(path);
    _database = await _initDatabase();
  }
}
