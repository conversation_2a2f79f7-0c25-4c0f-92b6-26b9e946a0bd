import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'okitchen.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create ingredients table
    await db.execute('''
      CREATE TABLE ingredients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        category TEXT NOT NULL,
        description TEXT,
        image_url TEXT
      )
    ''');

    // Create recipes table
    await db.execute('''
      CREATE TABLE recipes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        image_url TEXT,
        steps TEXT NOT NULL,
        cooking_time_minutes INTEGER NOT NULL,
        difficulty INTEGER NOT NULL,
        servings INTEGER NOT NULL,
        category TEXT,
        created_at TEXT,
        is_favorite INTEGER DEFAULT 0
      )
    ''');

    // Create recipe_ingredients table (many-to-many relationship)
    await db.execute('''
      CREATE TABLE recipe_ingredients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recipe_id INTEGER NOT NULL,
        ingredient_id INTEGER NOT NULL,
        quantity TEXT NOT NULL,
        unit TEXT,
        is_optional INTEGER DEFAULT 0,
        FOREIGN KEY (recipe_id) REFERENCES recipes (id) ON DELETE CASCADE,
        FOREIGN KEY (ingredient_id) REFERENCES ingredients (id) ON DELETE CASCADE,
        UNIQUE(recipe_id, ingredient_id)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_ingredients_category ON ingredients(category)');
    await db.execute('CREATE INDEX idx_recipes_category ON recipes(category)');
    await db.execute('CREATE INDEX idx_recipes_favorite ON recipes(is_favorite)');
    await db.execute('CREATE INDEX idx_recipe_ingredients_recipe ON recipe_ingredients(recipe_id)');
    await db.execute('CREATE INDEX idx_recipe_ingredients_ingredient ON recipe_ingredients(ingredient_id)');

    // Insert sample data
    await _insertSampleData(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  Future<void> _insertSampleData(Database db) async {
    // Insert sample ingredients
    final sampleIngredients = [
      {'name': 'Thịt gà', 'category': 'Thịt', 'description': 'Thịt gà tươi'},
      {'name': 'Hành tây', 'category': 'Rau củ', 'description': 'Hành tây tươi'},
      {'name': 'Muối', 'category': 'Gia vị', 'description': 'Muối ăn'},
      {'name': 'Tiêu', 'category': 'Gia vị', 'description': 'Tiêu đen'},
      {'name': 'Gạo', 'category': 'Ngũ cốc', 'description': 'Gạo tẻ'},
      {'name': 'Cà chua', 'category': 'Rau củ', 'description': 'Cà chua chín'},
      {'name': 'Trứng gà', 'category': 'Khác', 'description': 'Trứng gà tươi'},
      {'name': 'Dầu ăn', 'category': 'Gia vị', 'description': 'Dầu ăn thực vật'},
    ];

    for (final ingredient in sampleIngredients) {
      await db.insert('ingredients', ingredient);
    }

    // Insert sample recipes
    final sampleRecipes = [
      {
        'title': 'Gà xào hành tây',
        'description': 'Món gà xào đơn giản và ngon miệng',
        'steps': 'Thái gà miếng vừa|Phi thơm hành tây|Xào gà với hành tây|Nêm gia vị vừa ăn',
        'cooking_time_minutes': 30,
        'difficulty': 1,
        'servings': 2,
        'category': 'Món chính',
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'title': 'Cơm chiên trứng',
        'description': 'Cơm chiên trứng thơm ngon',
        'steps': 'Đánh trứng|Chiên trứng|Cho cơm vào xào|Nêm gia vị',
        'cooking_time_minutes': 15,
        'difficulty': 1,
        'servings': 1,
        'category': 'Món chính',
        'created_at': DateTime.now().toIso8601String(),
      },
    ];

    for (final recipe in sampleRecipes) {
      await db.insert('recipes', recipe);
    }

    // Insert sample recipe ingredients
    final sampleRecipeIngredients = [
      // Gà xào hành tây (recipe_id: 1)
      {'recipe_id': 1, 'ingredient_id': 1, 'quantity': '300', 'unit': 'gram'},
      {'recipe_id': 1, 'ingredient_id': 2, 'quantity': '1', 'unit': 'củ'},
      {'recipe_id': 1, 'ingredient_id': 3, 'quantity': '1', 'unit': 'tsp'},
      {'recipe_id': 1, 'ingredient_id': 4, 'quantity': '1/2', 'unit': 'tsp'},
      {'recipe_id': 1, 'ingredient_id': 8, 'quantity': '2', 'unit': 'tbsp'},
      
      // Cơm chiên trứng (recipe_id: 2)
      {'recipe_id': 2, 'ingredient_id': 5, 'quantity': '1', 'unit': 'chén'},
      {'recipe_id': 2, 'ingredient_id': 7, 'quantity': '2', 'unit': 'quả'},
      {'recipe_id': 2, 'ingredient_id': 3, 'quantity': '1/2', 'unit': 'tsp'},
      {'recipe_id': 2, 'ingredient_id': 8, 'quantity': '1', 'unit': 'tbsp'},
    ];

    for (final recipeIngredient in sampleRecipeIngredients) {
      await db.insert('recipe_ingredients', recipeIngredient);
    }
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  // Reset database (for testing purposes)
  Future<void> resetDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'okitchen.db');
    
    await close();
    await deleteDatabase(path);
    _database = await _initDatabase();
  }
}
