import 'package:flutter/material.dart';
import '../models/models.dart';

class IngredientItemTile extends StatelessWidget {
  final Ingredient ingredient;
  final bool isSelected;
  final VoidCallback onToggle;
  final Color categoryColor;

  const IngredientItemTile({
    super.key,
    required this.ingredient,
    required this.isSelected,
    required this.onToggle,
    required this.categoryColor,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: _buildLeading(context),
      title: Text(
        ingredient.name,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          color: isSelected 
              ? categoryColor 
              : Theme.of(context).colorScheme.onSurface,
        ),
      ),
      subtitle: ingredient.description != null
          ? Text(
              ingredient.description!,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      trailing: _buildTrailing(context),
      onTap: onToggle,
    );
  }

  Widget _buildLeading(BuildContext context) {
    if (ingredient.imageUrl != null && ingredient.imageUrl!.isNotEmpty) {
      // If ingredient has image URL, show image
      return CircleAvatar(
        radius: 20,
        backgroundColor: categoryColor.withOpacity(0.1),
        backgroundImage: NetworkImage(ingredient.imageUrl!),
        onBackgroundImageError: (_, __) {
          // Fallback to text avatar if image fails to load
        },
        child: ingredient.imageUrl!.isEmpty
            ? Text(
                ingredient.name[0].toUpperCase(),
                style: TextStyle(
                  color: categoryColor,
                  fontWeight: FontWeight.w600,
                ),
              )
            : null,
      );
    } else {
      // Show text avatar with first letter
      return CircleAvatar(
        radius: 20,
        backgroundColor: isSelected 
            ? categoryColor.withOpacity(0.2)
            : categoryColor.withOpacity(0.1),
        child: Text(
          ingredient.name[0].toUpperCase(),
          style: TextStyle(
            color: isSelected ? categoryColor : categoryColor.withOpacity(0.7),
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }
  }

  Widget _buildTrailing(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: Checkbox(
        value: isSelected,
        onChanged: (_) => onToggle,
        activeColor: categoryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }
}
