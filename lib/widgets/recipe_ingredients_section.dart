import 'package:flutter/material.dart';
import '../models/models.dart';

class RecipeIngredientsSection extends StatelessWidget {
  final List<IngredientWithQuantity> ingredients;

  const RecipeIngredientsSection({
    super.key,
    required this.ingredients,
  });

  @override
  Widget build(BuildContext context) {
    if (ingredients.isEmpty) {
      return const SizedBox.shrink();
    }

    final requiredIngredients = ingredients.where((i) => !i.isOptional).toList();
    final optionalIngredients = ingredients.where((i) => i.isOptional).toList();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.shopping_cart,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Nguyên liệu',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                _buildIngredientsSummary(context),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Required ingredients
          if (requiredIngredients.isNotEmpty) ...[
            _buildIngredientsGroup(
              context,
              'Nguyên liệu chính',
              requiredIngredients,
              false,
            ),
          ],
          
          // Optional ingredients
          if (optionalIngredients.isNotEmpty) ...[
            if (requiredIngredients.isNotEmpty) const Divider(height: 1),
            _buildIngredientsGroup(
              context,
              'Nguyên liệu tùy chọn',
              optionalIngredients,
              true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildIngredientsSummary(BuildContext context) {
    final availableCount = ingredients.where((i) => i.isAvailable).length;
    final totalCount = ingredients.length;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: availableCount == totalCount
            ? Colors.green.withOpacity(0.1)
            : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$availableCount/$totalCount',
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: availableCount == totalCount ? Colors.green : Colors.orange,
        ),
      ),
    );
  }

  Widget _buildIngredientsGroup(
    BuildContext context,
    String title,
    List<IngredientWithQuantity> groupIngredients,
    bool isOptional,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Group title
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
          child: Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              if (isOptional) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Tùy chọn',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Ingredients list
        ...groupIngredients.map((ingredient) => _buildIngredientItem(context, ingredient)),
        
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildIngredientItem(BuildContext context, IngredientWithQuantity ingredient) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ingredient.isAvailable
            ? Colors.green.withOpacity(0.05)
            : Colors.red.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ingredient.isAvailable
              ? Colors.green.withOpacity(0.2)
              : Colors.red.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Status icon
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: ingredient.isAvailable
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              ingredient.isAvailable ? Icons.check : Icons.close,
              size: 16,
              color: ingredient.isAvailable ? Colors.green : Colors.red,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Ingredient avatar
          CircleAvatar(
            radius: 16,
            backgroundColor: ingredient.isAvailable
                ? Colors.green.withOpacity(0.1)
                : Colors.grey.withOpacity(0.1),
            backgroundImage: ingredient.imageUrl != null
                ? NetworkImage(ingredient.imageUrl!)
                : null,
            child: ingredient.imageUrl == null
                ? Text(
                    ingredient.name[0].toUpperCase(),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: ingredient.isAvailable ? Colors.green : Colors.grey,
                    ),
                  )
                : null,
          ),
          
          const SizedBox(width: 12),
          
          // Ingredient info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ingredient.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: ingredient.isAvailable
                        ? Theme.of(context).colorScheme.onSurface
                        : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    decoration: ingredient.isAvailable
                        ? null
                        : TextDecoration.lineThrough,
                  ),
                ),
                Text(
                  ingredient.category,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          
          // Quantity
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              ingredient.displayQuantity,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
