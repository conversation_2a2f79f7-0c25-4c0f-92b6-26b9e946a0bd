import 'package:flutter/material.dart';
import '../models/models.dart';

class FilterBottomSheet extends StatefulWidget {
  final String selectedFilter;
  final DifficultyLevel? difficultyFilter;
  final int? maxCookingTime;
  final String? categoryFilter;
  final Function(String, DifficultyLevel?, int?, String?) onFiltersChanged;

  const FilterBottomSheet({
    super.key,
    required this.selectedFilter,
    required this.difficultyFilter,
    required this.maxCookingTime,
    required this.categoryFilter,
    required this.onFiltersChanged,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late String _selectedFilter;
  DifficultyLevel? _difficultyFilter;
  int? _maxCookingTime;
  String? _categoryFilter;

  final List<int> _cookingTimeOptions = [15, 30, 60, 90, 120];
  final List<String> _categoryOptions = [
    '<PERSON><PERSON> chính',
    '<PERSON>ón phụ',
    '<PERSON><PERSON> tráng miệng',
    '<PERSON><PERSON> uống',
    'Món ăn sáng',
  ];

  @override
  void initState() {
    super.initState();
    _selectedFilter = widget.selectedFilter;
    _difficultyFilter = widget.difficultyFilter;
    _maxCookingTime = widget.maxCookingTime;
    _categoryFilter = widget.categoryFilter;
  }

  void _applyFilters() {
    widget.onFiltersChanged(
      _selectedFilter,
      _difficultyFilter,
      _maxCookingTime,
      _categoryFilter,
    );
    Navigator.of(context).pop();
  }

  void _resetFilters() {
    setState(() {
      _selectedFilter = 'all';
      _difficultyFilter = null;
      _maxCookingTime = null;
      _categoryFilter = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Text(
                'Bộ lọc',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('Đặt lại'),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Match status filter
          _buildMatchStatusFilter(),
          
          const SizedBox(height: 24),
          
          // Difficulty filter
          _buildDifficultyFilter(),
          
          const SizedBox(height: 24),
          
          // Cooking time filter
          _buildCookingTimeFilter(),
          
          const SizedBox(height: 24),
          
          // Category filter
          _buildCategoryFilter(),
          
          const SizedBox(height: 24),
          
          // Apply button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _applyFilters,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Áp dụng'),
            ),
          ),
          
          // Safe area padding
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildMatchStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Mức độ phù hợp',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildFilterChip(
              'all',
              'Tất cả',
              _selectedFilter == 'all',
              () => setState(() => _selectedFilter = 'all'),
            ),
            _buildFilterChip(
              'perfect',
              '✅ Hoàn hảo',
              _selectedFilter == 'perfect',
              () => setState(() => _selectedFilter = 'perfect'),
            ),
            _buildFilterChip(
              'good',
              '✅ Tốt',
              _selectedFilter == 'good',
              () => setState(() => _selectedFilter = 'good'),
            ),
            _buildFilterChip(
              'partial',
              '⚠️ Một phần',
              _selectedFilter == 'partial',
              () => setState(() => _selectedFilter = 'partial'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDifficultyFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Độ khó',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildFilterChip(
              null,
              'Tất cả',
              _difficultyFilter == null,
              () => setState(() => _difficultyFilter = null),
            ),
            ...DifficultyLevel.values.map((difficulty) =>
              _buildFilterChip(
                difficulty,
                difficulty.displayName,
                _difficultyFilter == difficulty,
                () => setState(() => _difficultyFilter = difficulty),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCookingTimeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thời gian nấu (tối đa)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildFilterChip(
              null,
              'Không giới hạn',
              _maxCookingTime == null,
              () => setState(() => _maxCookingTime = null),
            ),
            ..._cookingTimeOptions.map((time) =>
              _buildFilterChip(
                time,
                '$time phút',
                _maxCookingTime == time,
                () => setState(() => _maxCookingTime = time),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Loại món',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildFilterChip(
              null,
              'Tất cả',
              _categoryFilter == null,
              () => setState(() => _categoryFilter = null),
            ),
            ..._categoryOptions.map((category) =>
              _buildFilterChip(
                category,
                category,
                _categoryFilter == category,
                () => setState(() => _categoryFilter = category),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterChip(
    dynamic value,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor: Theme.of(context).colorScheme.primaryContainer,
      checkmarkColor: Theme.of(context).colorScheme.onPrimaryContainer,
      labelStyle: TextStyle(
        color: isSelected
            ? Theme.of(context).colorScheme.onPrimaryContainer
            : Theme.of(context).colorScheme.onSurface,
        fontSize: 12,
      ),
    );
  }
}
