import 'package:flutter/material.dart';
import '../models/models.dart';

class RecipeMatchCard extends StatelessWidget {
  final RecipeMatch match;
  final VoidCallback onTap;

  const RecipeMatchCard({
    super.key,
    required this.match,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Recipe image and match status
            _buildImageHeader(context),
            
            // Recipe info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and favorite button
                  _buildTitleRow(context),
                  
                  const SizedBox(height: 8),
                  
                  // Recipe details
                  _buildRecipeDetails(context),
                  
                  const SizedBox(height: 12),
                  
                  // Ingredients summary
                  _buildIngredientsSummary(context),
                  
                  const SizedBox(height: 12),
                  
                  // Match percentage and missing ingredients
                  _buildMatchInfo(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageHeader(BuildContext context) {
    return Stack(
      children: [
        // Recipe image
        Container(
          height: 160,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant,
            image: match.recipe.imageUrl != null
                ? DecorationImage(
                    image: NetworkImage(match.recipe.imageUrl!),
                    fit: BoxFit.cover,
                    onError: (_, __) {
                      // Handle image loading error
                    },
                  )
                : null,
          ),
          child: match.recipe.imageUrl == null
              ? const Icon(
                  Icons.restaurant,
                  size: 48,
                  color: Colors.grey,
                )
              : null,
        ),
        
        // Match status badge
        Positioned(
          top: 12,
          right: 12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getMatchStatusColor(context),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  match.matchStatus.icon,
                  style: const TextStyle(fontSize: 12),
                ),
                const SizedBox(width: 4),
                Text(
                  '${match.matchPercentage.round()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitleRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            match.recipe.title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        IconButton(
          onPressed: () {
            // TODO: Toggle favorite
          },
          icon: Icon(
            match.recipe.isFavorite ? Icons.favorite : Icons.favorite_border,
            color: match.recipe.isFavorite ? Colors.red : null,
          ),
        ),
      ],
    );
  }

  Widget _buildRecipeDetails(BuildContext context) {
    return Row(
      children: [
        // Cooking time
        Icon(
          Icons.access_time,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 4),
        Text(
          '${match.recipe.cookingTimeMinutes} phút',
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Difficulty
        Icon(
          Icons.bar_chart,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 4),
        Text(
          match.recipe.difficulty.displayName,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Servings
        Icon(
          Icons.people,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 4),
        Text(
          '${match.recipe.servings} người',
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildIngredientsSummary(BuildContext context) {
    final availableIngredients = match.availableIngredients;
    final missingRequired = match.missingRequiredIngredients;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Available ingredients
        if (availableIngredients.isNotEmpty)
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: availableIngredients.take(3).map((ingredient) {
              return Chip(
                label: Text(ingredient.name),
                backgroundColor: Colors.green.withOpacity(0.1),
                labelStyle: const TextStyle(
                  fontSize: 10,
                  color: Colors.green,
                ),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              );
            }).toList(),
          ),
        
        // Show more available ingredients indicator
        if (availableIngredients.length > 3)
          Text(
            '+${availableIngredients.length - 3} nguyên liệu khác',
            style: TextStyle(
              fontSize: 10,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
      ],
    );
  }

  Widget _buildMatchInfo(BuildContext context) {
    final missingRequired = match.missingRequiredIngredients;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getMatchStatusColor(context).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getMatchStatusIcon(),
            size: 16,
            color: _getMatchStatusColor(context),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  match.matchStatus.displayName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: _getMatchStatusColor(context),
                  ),
                ),
                if (missingRequired.isNotEmpty)
                  Text(
                    'Thiếu: ${missingRequired.map((i) => i.name).join(', ')}',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          Text(
            '${match.availableIngredientsCount}/${match.totalIngredientsCount}',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: _getMatchStatusColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Color _getMatchStatusColor(BuildContext context) {
    switch (match.matchStatus) {
      case RecipeMatchStatus.perfect:
      case RecipeMatchStatus.good:
        return Colors.green;
      case RecipeMatchStatus.partial:
        return Colors.orange;
      case RecipeMatchStatus.poor:
        return Colors.red;
    }
  }

  IconData _getMatchStatusIcon() {
    switch (match.matchStatus) {
      case RecipeMatchStatus.perfect:
      case RecipeMatchStatus.good:
        return Icons.check_circle;
      case RecipeMatchStatus.partial:
        return Icons.warning;
      case RecipeMatchStatus.poor:
        return Icons.error;
    }
  }
}
