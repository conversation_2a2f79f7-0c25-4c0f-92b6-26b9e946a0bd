import 'package:flutter/material.dart';
import '../models/models.dart';

class RecipeNutritionTips extends StatelessWidget {
  final Recipe recipe;
  final List<IngredientWithQuantity> ingredients;

  const RecipeNutritionTips({
    super.key,
    required this.recipe,
    required this.ingredients,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Mẹo và dinh dưỡng',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Cooking tips
                _buildCookingTips(context),
                
                const SizedBox(height: 16),
                
                // Nutrition info
                _buildNutritionInfo(context),
                
                const SizedBox(height: 16),
                
                // Storage tips
                _buildStorageTips(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCookingTips(BuildContext context) {
    final tips = _getCookingTips();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.tips_and_updates,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Mẹo nấu ăn',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...tips.map((tip) => Padding(
          padding: const EdgeInsets.only(bottom: 6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 6),
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  tip,
                  style: const TextStyle(fontSize: 13, height: 1.4),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildNutritionInfo(BuildContext context) {
    final nutritionInfo = _getNutritionInfo();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.health_and_safety,
              size: 16,
              color: Colors.green,
            ),
            const SizedBox(width: 8),
            const Text(
              'Thông tin dinh dưỡng',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: nutritionInfo.map((info) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.green.withOpacity(0.3),
              ),
            ),
            child: Text(
              info,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.green,
                fontWeight: FontWeight.w500,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildStorageTips(BuildContext context) {
    final storageTips = _getStorageTips();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.kitchen,
              size: 16,
              color: Colors.orange,
            ),
            const SizedBox(width: 8),
            const Text(
              'Bảo quản và phục vụ',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...storageTips.map((tip) => Padding(
          padding: const EdgeInsets.only(bottom: 6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 6),
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  tip,
                  style: const TextStyle(fontSize: 13, height: 1.4),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  List<String> _getCookingTips() {
    final List<String> tips = [];
    
    // General tips based on recipe category
    switch (recipe.category?.toLowerCase()) {
      case 'món chính':
        tips.add('Nêm nếm gia vị từ từ, có thể điều chỉnh theo khẩu vị');
        tips.add('Để thịt nghỉ 5-10 phút sau khi nấu để giữ độ mềm');
        break;
      case 'canh':
        tips.add('Nấu canh với lửa vừa để giữ nước trong');
        tips.add('Cho rau lá vào cuối cùng để giữ màu xanh');
        break;
      case 'lẩu':
        tips.add('Luôn giữ nước lẩu sôi nhẹ khi ăn');
        tips.add('Cho hải sản vào sau, rau củ vào trước');
        break;
    }
    
    // Tips based on cooking time
    if (recipe.cookingTimeMinutes > 60) {
      tips.add('Món này cần thời gian, hãy chuẩn bị từ sớm');
      tips.add('Có thể chuẩn bị nguyên liệu từ trước để tiết kiệm thời gian');
    }
    
    // Tips based on difficulty
    switch (recipe.difficulty) {
      case DifficultyLevel.easy:
        tips.add('Món này dễ làm, phù hợp cho người mới bắt đầu');
        break;
      case DifficultyLevel.medium:
        tips.add('Chú ý thời gian và nhiệt độ để món ăn hoàn hảo');
        break;
      case DifficultyLevel.hard:
        tips.add('Đọc kỹ hướng dẫn trước khi bắt đầu');
        tips.add('Chuẩn bị đầy đủ dụng cụ và nguyên liệu');
        break;
    }
    
    return tips.isEmpty ? ['Làm theo hướng dẫn từng bước để có kết quả tốt nhất'] : tips;
  }

  List<String> _getNutritionInfo() {
    final List<String> nutrition = [];
    
    // Analyze ingredients for nutrition info
    final hasVegetables = ingredients.any((i) => i.category == 'Rau củ');
    final hasMeat = ingredients.any((i) => i.category == 'Thịt');
    final hasSeafood = ingredients.any((i) => i.category == 'Hải sản');
    final hasGrains = ingredients.any((i) => i.category == 'Ngũ cốc');
    
    if (hasVegetables) nutrition.add('Giàu vitamin');
    if (hasMeat || hasSeafood) nutrition.add('Protein cao');
    if (hasGrains) nutrition.add('Cung cấp carbohydrate');
    if (ingredients.any((i) => i.name.contains('gừng'))) nutrition.add('Tốt cho tiêu hóa');
    if (ingredients.any((i) => i.name.contains('tỏi'))) nutrition.add('Tăng cường miễn dịch');
    
    // Based on cooking method
    if (recipe.steps.any((step) => step.contains('luộc') || step.contains('hấp'))) {
      nutrition.add('Ít dầu mỡ');
    }
    
    return nutrition.isEmpty ? ['Cân bằng dinh dưỡng'] : nutrition;
  }

  List<String> _getStorageTips() {
    final List<String> tips = [];
    
    // General storage tips
    tips.add('Bảo quản trong tủ lạnh tối đa 2-3 ngày');
    tips.add('Hâm nóng kỹ trước khi ăn');
    
    // Specific tips based on ingredients
    if (ingredients.any((i) => i.category == 'Hải sản')) {
      tips.add('Món có hải sản nên ăn ngay, không để qua đêm');
    }
    
    if (ingredients.any((i) => i.category == 'Rau củ')) {
      tips.add('Rau lá có thể bị héo, nên ăn trong ngày');
    }
    
    // Serving suggestions
    switch (recipe.category?.toLowerCase()) {
      case 'món chính':
        tips.add('Ăn kèm cơm trắng hoặc bánh mì');
        break;
      case 'canh':
        tips.add('Phục vụ nóng cùng bữa cơm');
        break;
      case 'lẩu':
        tips.add('Ăn ngay khi còn nóng, kèm rau sống');
        break;
    }
    
    return tips;
  }
}
