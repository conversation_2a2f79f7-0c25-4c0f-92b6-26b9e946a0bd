import 'package:flutter/material.dart';
import 'dart:async';

class EnhancedRecipeStepsSection extends StatefulWidget {
  final List<String> steps;

  const EnhancedRecipeStepsSection({
    super.key,
    required this.steps,
  });

  @override
  State<EnhancedRecipeStepsSection> createState() => _EnhancedRecipeStepsSectionState();
}

class _EnhancedRecipeStepsSectionState extends State<EnhancedRecipeStepsSection> {
  final Set<int> _completedSteps = {};
  final Map<int, Timer?> _stepTimers = {};
  final Map<int, int> _stepTimeRemaining = {};
  final Map<int, bool> _stepTimerActive = {};

  @override
  void dispose() {
    // Cancel all timers
    for (final timer in _stepTimers.values) {
      timer?.cancel();
    }
    super.dispose();
  }

  void _toggleStepCompletion(int stepIndex) {
    setState(() {
      if (_completedSteps.contains(stepIndex)) {
        _completedSteps.remove(stepIndex);
      } else {
        _completedSteps.add(stepIndex);
        // Stop timer if step is completed
        _stopTimer(stepIndex);
      }
    });
  }

  void _startTimer(int stepIndex, int minutes) {
    if (_stepTimers[stepIndex]?.isActive == true) return;

    setState(() {
      _stepTimeRemaining[stepIndex] = minutes * 60; // Convert to seconds
      _stepTimerActive[stepIndex] = true;
    });

    _stepTimers[stepIndex] = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_stepTimeRemaining[stepIndex]! > 0) {
          _stepTimeRemaining[stepIndex] = _stepTimeRemaining[stepIndex]! - 1;
        } else {
          timer.cancel();
          _stepTimerActive[stepIndex] = false;
          // Show notification when timer ends
          _showTimerCompleteDialog(stepIndex);
        }
      });
    });
  }

  void _stopTimer(int stepIndex) {
    _stepTimers[stepIndex]?.cancel();
    setState(() {
      _stepTimerActive[stepIndex] = false;
      _stepTimeRemaining[stepIndex] = 0;
    });
  }

  void _showTimerCompleteDialog(int stepIndex) {
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⏰ Hết giờ!'),
        content: Text('Bước ${stepIndex + 1} đã hoàn thành thời gian.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Extract time from step text (e.g., "nấu 15 phút" -> 15)
  int? _extractTimeFromStep(String step) {
    final RegExp timeRegex = RegExp(r'(\d+)\s*phút');
    final match = timeRegex.firstMatch(step.toLowerCase());
    if (match != null) {
      return int.tryParse(match.group(1)!);
    }
    return null;
  }

  // Extract cooking tips from step text
  List<String> _extractTipsFromStep(String step) {
    final List<String> tips = [];
    
    if (step.contains('lửa nhỏ')) {
      tips.add('💡 Dùng lửa nhỏ để tránh cháy');
    }
    if (step.contains('đảo đều')) {
      tips.add('💡 Đảo đều tay để thức ăn chín đồng đều');
    }
    if (step.contains('nước sôi')) {
      tips.add('💡 Đợi nước sôi bùng bùng mới cho nguyên liệu');
    }
    if (step.contains('ướp')) {
      tips.add('💡 Ướp càng lâu thịt càng thấm gia vị');
    }
    if (step.contains('phi thơm')) {
      tips.add('💡 Phi thơm đến khi có mùi thơm nức');
    }
    
    return tips;
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    if (widget.steps.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.restaurant_menu,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Hướng dẫn chi tiết',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                _buildProgressIndicator(context),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Steps list
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            itemCount: widget.steps.length,
            separatorBuilder: (context, index) => const SizedBox(height: 20),
            itemBuilder: (context, index) {
              return _buildEnhancedStepItem(context, index, widget.steps[index]);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    final completedCount = _completedSteps.length;
    final totalCount = widget.steps.length;
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 40,
          height: 40,
          child: Stack(
            children: [
              CircularProgressIndicator(
                value: progress,
                backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
                strokeWidth: 3,
              ),
              Center(
                child: Text(
                  '$completedCount/$totalCount',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedStepItem(BuildContext context, int index, String step) {
    final isCompleted = _completedSteps.contains(index);
    final stepNumber = index + 1;
    final timeInMinutes = _extractTimeFromStep(step);
    final tips = _extractTipsFromStep(step);
    final isTimerActive = _stepTimerActive[index] == true;
    final timeRemaining = _stepTimeRemaining[index] ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCompleted
            ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3)
            : Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCompleted
              ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
              : Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step header with number and timer
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Step number/checkbox
              GestureDetector(
                onTap: () => _toggleStepCompletion(index),
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isCompleted
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline,
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: isCompleted
                        ? Icon(
                            Icons.check,
                            size: 18,
                            color: Theme.of(context).colorScheme.onPrimary,
                          )
                        : Text(
                            '$stepNumber',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Step content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Bước $stepNumber',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const Spacer(),
                        // Timer controls
                        if (timeInMinutes != null && !isCompleted) ...[
                          if (isTimerActive) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.orange.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.timer, size: 14, color: Colors.orange),
                                  const SizedBox(width: 4),
                                  Text(
                                    _formatTime(timeRemaining),
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.orange,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: () => _stopTimer(index),
                              icon: const Icon(Icons.stop, size: 16),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                            ),
                          ] else ...[
                            IconButton(
                              onPressed: () => _startTimer(index, timeInMinutes),
                              icon: const Icon(Icons.timer, size: 16),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                              tooltip: 'Bắt đầu đếm ngược $timeInMinutes phút',
                            ),
                          ],
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      step,
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.4,
                        color: isCompleted
                            ? Theme.of(context).colorScheme.onSurface.withOpacity(0.7)
                            : Theme.of(context).colorScheme.onSurface,
                        decoration: isCompleted
                            ? TextDecoration.lineThrough
                            : null,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          // Tips section
          if (tips.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: tips.map((tip) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    tip,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                  ),
                )).toList(),
              ),
            ),
          ],
          
          // Completion indicator
          if (isCompleted) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 14,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Hoàn thành',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
