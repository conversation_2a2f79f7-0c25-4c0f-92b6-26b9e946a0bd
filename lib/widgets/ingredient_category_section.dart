import 'package:flutter/material.dart';
import '../models/models.dart';
import 'ingredient_item_tile.dart';

class IngredientCategorySection extends StatefulWidget {
  final String category;
  final List<Ingredient> ingredients;
  final UserIngredientSelection userSelection;
  final Function(int) onIngredientToggle;

  const IngredientCategorySection({
    super.key,
    required this.category,
    required this.ingredients,
    required this.userSelection,
    required this.onIngredientToggle,
  });

  @override
  State<IngredientCategorySection> createState() => _IngredientCategorySectionState();
}

class _IngredientCategorySectionState extends State<IngredientCategorySection> {
  bool _isExpanded = true;

  // Get category icon based on category name
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'rau củ':
        return Icons.eco;
      case 'thịt':
        return Icons.set_meal;
      case 'hải sản':
        return Icons.phishing;
      case 'gia vị':
        return Icons.grain;
      case 'ngũ cốc':
        return Icons.rice_bowl;
      case 'sữa và chế phẩm':
        return Icons.local_drink;
      case 'trái cây':
        return Icons.apple;
      default:
        return Icons.category;
    }
  }

  // Get category color based on category name
  Color _getCategoryColor(BuildContext context, String category) {
    final colorScheme = Theme.of(context).colorScheme;
    
    switch (category.toLowerCase()) {
      case 'rau củ':
        return Colors.green;
      case 'thịt':
        return Colors.red;
      case 'hải sản':
        return Colors.blue;
      case 'gia vị':
        return Colors.orange;
      case 'ngũ cốc':
        return Colors.brown;
      case 'sữa và chế phẩm':
        return Colors.indigo;
      case 'trái cây':
        return Colors.pink;
      default:
        return colorScheme.primary;
    }
  }

  int get _selectedCount {
    return widget.ingredients
        .where((ingredient) => widget.userSelection.isSelected(ingredient.id!))
        .length;
  }

  void _toggleAll() {
    final bool shouldSelectAll = _selectedCount < widget.ingredients.length;
    
    for (final ingredient in widget.ingredients) {
      final isCurrentlySelected = widget.userSelection.isSelected(ingredient.id!);
      
      if (shouldSelectAll && !isCurrentlySelected) {
        widget.onIngredientToggle(ingredient.id!);
      } else if (!shouldSelectAll && isCurrentlySelected) {
        widget.onIngredientToggle(ingredient.id!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoryColor = _getCategoryColor(context, widget.category);
    final categoryIcon = _getCategoryIcon(widget.category);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          // Category header
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: categoryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  // Category icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: categoryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      categoryIcon,
                      color: categoryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Category name and count
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.category,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${widget.ingredients.length} nguyên liệu',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Selected count badge
                  if (_selectedCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: categoryColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$_selectedCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  
                  const SizedBox(width: 8),
                  
                  // Select all button
                  IconButton(
                    onPressed: _toggleAll,
                    icon: Icon(
                      _selectedCount == widget.ingredients.length
                          ? Icons.check_box
                          : _selectedCount > 0
                              ? Icons.indeterminate_check_box
                              : Icons.check_box_outline_blank,
                      color: categoryColor,
                    ),
                    tooltip: _selectedCount == widget.ingredients.length
                        ? 'Bỏ chọn tất cả'
                        : 'Chọn tất cả',
                  ),
                  
                  // Expand/collapse icon
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
            ),
          ),
          
          // Ingredients list
          if (_isExpanded)
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: widget.ingredients.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final ingredient = widget.ingredients[index];
                final isSelected = widget.userSelection.isSelected(ingredient.id!);

                return IngredientItemTile(
                  ingredient: ingredient,
                  isSelected: isSelected,
                  onToggle: () => widget.onIngredientToggle(ingredient.id!),
                  categoryColor: categoryColor,
                );
              },
            ),
        ],
      ),
    );
  }
}
