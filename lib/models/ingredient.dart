class Ingredient {
  final int? id;
  final String name;
  final String category;
  final String? description;
  final String? imageUrl;

  const Ingredient({
    this.id,
    required this.name,
    required this.category,
    this.description,
    this.imageUrl,
  });

  // Convert from Map (database row) to Ingredient object
  factory Ingredient.fromMap(Map<String, dynamic> map) {
    return Ingredient(
      id: map['id'] as int?,
      name: map['name'] as String,
      category: map['category'] as String,
      description: map['description'] as String?,
      imageUrl: map['image_url'] as String?,
    );
  }

  // Convert from Ingredient object to Map (for database insertion)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'image_url': imageUrl,
    };
  }

  // Create a copy with modified fields
  Ingredient copyWith({
    int? id,
    String? name,
    String? category,
    String? description,
    String? imageUrl,
  }) {
    return Ingredient(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  @override
  String toString() {
    return 'Ingredient{id: $id, name: $name, category: $category, description: $description, imageUrl: $imageUrl}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Ingredient &&
        other.id == id &&
        other.name == name &&
        other.category == category &&
        other.description == description &&
        other.imageUrl == imageUrl;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        category.hashCode ^
        description.hashCode ^
        imageUrl.hashCode;
  }
}

// Enum for ingredient categories
enum IngredientCategory {
  vegetables('Rau củ'),
  meat('Thịt'),
  seafood('Hải sản'),
  spices('Gia vị'),
  grains('Ngũ cốc'),
  dairy('Sữa và chế phẩm'),
  fruits('Trái cây'),
  others('Khác');

  const IngredientCategory(this.displayName);
  final String displayName;

  static IngredientCategory fromString(String category) {
    return IngredientCategory.values.firstWhere(
      (e) => e.displayName == category,
      orElse: () => IngredientCategory.others,
    );
  }
}
