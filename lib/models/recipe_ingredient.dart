class RecipeIngredient {
  final int? id;
  final int recipeId;
  final int ingredientId;
  final String quantity;
  final String? unit;
  final bool isOptional;

  const RecipeIngredient({
    this.id,
    required this.recipeId,
    required this.ingredientId,
    required this.quantity,
    this.unit,
    this.isOptional = false,
  });

  // Convert from Map (database row) to RecipeIngredient object
  factory RecipeIngredient.fromMap(Map<String, dynamic> map) {
    return RecipeIngredient(
      id: map['id'] as int?,
      recipeId: map['recipe_id'] as int,
      ingredientId: map['ingredient_id'] as int,
      quantity: map['quantity'] as String,
      unit: map['unit'] as String?,
      isOptional: (map['is_optional'] as int?) == 1,
    );
  }

  // Convert from RecipeIngredient object to Map (for database insertion)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'recipe_id': recipeId,
      'ingredient_id': ingredientId,
      'quantity': quantity,
      'unit': unit,
      'is_optional': isOptional ? 1 : 0,
    };
  }

  // Create a copy with modified fields
  RecipeIngredient copyWith({
    int? id,
    int? recipeId,
    int? ingredientId,
    String? quantity,
    String? unit,
    bool? isOptional,
  }) {
    return RecipeIngredient(
      id: id ?? this.id,
      recipeId: recipeId ?? this.recipeId,
      ingredientId: ingredientId ?? this.ingredientId,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      isOptional: isOptional ?? this.isOptional,
    );
  }

  @override
  String toString() {
    return 'RecipeIngredient{id: $id, recipeId: $recipeId, ingredientId: $ingredientId, quantity: $quantity, unit: $unit, isOptional: $isOptional}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecipeIngredient &&
        other.id == id &&
        other.recipeId == recipeId &&
        other.ingredientId == ingredientId &&
        other.quantity == quantity &&
        other.unit == unit &&
        other.isOptional == isOptional;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        recipeId.hashCode ^
        ingredientId.hashCode ^
        quantity.hashCode ^
        unit.hashCode ^
        isOptional.hashCode;
  }
}

// Class to represent ingredient with quantity for display purposes
class IngredientWithQuantity {
  final int ingredientId;
  final String name;
  final String category;
  final String quantity;
  final String? unit;
  final bool isOptional;
  final bool isAvailable; // Whether user has this ingredient
  final String? imageUrl;

  const IngredientWithQuantity({
    required this.ingredientId,
    required this.name,
    required this.category,
    required this.quantity,
    this.unit,
    this.isOptional = false,
    this.isAvailable = false,
    this.imageUrl,
  });

  // Create from RecipeIngredient and Ingredient
  factory IngredientWithQuantity.fromRecipeIngredient(
    RecipeIngredient recipeIngredient,
    String ingredientName,
    String ingredientCategory,
    String? ingredientImageUrl,
    bool isAvailable,
  ) {
    return IngredientWithQuantity(
      ingredientId: recipeIngredient.ingredientId,
      name: ingredientName,
      category: ingredientCategory,
      quantity: recipeIngredient.quantity,
      unit: recipeIngredient.unit,
      isOptional: recipeIngredient.isOptional,
      isAvailable: isAvailable,
      imageUrl: ingredientImageUrl,
    );
  }

  String get displayQuantity {
    if (unit != null && unit!.isNotEmpty) {
      return '$quantity $unit';
    }
    return quantity;
  }

  @override
  String toString() {
    return 'IngredientWithQuantity{ingredientId: $ingredientId, name: $name, quantity: $displayQuantity, isOptional: $isOptional, isAvailable: $isAvailable}';
  }
}
