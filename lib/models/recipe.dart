class Recipe {
  final int? id;
  final String title;
  final String? description;
  final String? imageUrl;
  final List<String> steps;
  final int cookingTimeMinutes;
  final DifficultyLevel difficulty;
  final int servings;
  final String? category;
  final DateTime? createdAt;
  final bool isFavorite;

  const Recipe({
    this.id,
    required this.title,
    this.description,
    this.imageUrl,
    required this.steps,
    required this.cookingTimeMinutes,
    required this.difficulty,
    required this.servings,
    this.category,
    this.createdAt,
    this.isFavorite = false,
  });

  // Convert from Map (database row) to Recipe object
  factory Recipe.fromMap(Map<String, dynamic> map) {
    return Recipe(
      id: map['id'] as int?,
      title: map['title'] as String,
      description: map['description'] as String?,
      imageUrl: map['image_url'] as String?,
      steps: (map['steps'] as String).split('|'),
      cookingTimeMinutes: map['cooking_time_minutes'] as int,
      difficulty: DifficultyLevel.fromInt(map['difficulty'] as int),
      servings: map['servings'] as int,
      category: map['category'] as String?,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : null,
      isFavorite: (map['is_favorite'] as int?) == 1,
    );
  }

  // Convert from Recipe object to Map (for database insertion)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_url': imageUrl,
      'steps': steps.join('|'),
      'cooking_time_minutes': cookingTimeMinutes,
      'difficulty': difficulty.value,
      'servings': servings,
      'category': category,
      'created_at': createdAt?.toIso8601String(),
      'is_favorite': isFavorite ? 1 : 0,
    };
  }

  // Create a copy with modified fields
  Recipe copyWith({
    int? id,
    String? title,
    String? description,
    String? imageUrl,
    List<String>? steps,
    int? cookingTimeMinutes,
    DifficultyLevel? difficulty,
    int? servings,
    String? category,
    DateTime? createdAt,
    bool? isFavorite,
  }) {
    return Recipe(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      steps: steps ?? this.steps,
      cookingTimeMinutes: cookingTimeMinutes ?? this.cookingTimeMinutes,
      difficulty: difficulty ?? this.difficulty,
      servings: servings ?? this.servings,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  @override
  String toString() {
    return 'Recipe{id: $id, title: $title, cookingTimeMinutes: $cookingTimeMinutes, difficulty: $difficulty, servings: $servings, isFavorite: $isFavorite}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recipe &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.imageUrl == imageUrl &&
        other.cookingTimeMinutes == cookingTimeMinutes &&
        other.difficulty == difficulty &&
        other.servings == servings &&
        other.category == category &&
        other.isFavorite == isFavorite;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        description.hashCode ^
        imageUrl.hashCode ^
        cookingTimeMinutes.hashCode ^
        difficulty.hashCode ^
        servings.hashCode ^
        category.hashCode ^
        isFavorite.hashCode;
  }
}

// Enum for difficulty levels
enum DifficultyLevel {
  easy(1, 'Dễ'),
  medium(2, 'Trung bình'),
  hard(3, 'Khó');

  const DifficultyLevel(this.value, this.displayName);
  final int value;
  final String displayName;

  static DifficultyLevel fromInt(int value) {
    return DifficultyLevel.values.firstWhere(
      (e) => e.value == value,
      orElse: () => DifficultyLevel.easy,
    );
  }
}
