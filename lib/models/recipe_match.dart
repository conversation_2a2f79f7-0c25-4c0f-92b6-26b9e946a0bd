import 'recipe.dart';
import 'recipe_ingredient.dart';

// Class to represent a recipe with matching information
class RecipeMatch {
  final Recipe recipe;
  final List<IngredientWithQuantity> ingredients;
  final double matchPercentage;
  final int availableIngredientsCount;
  final int totalIngredientsCount;
  final int missingIngredientsCount;

  const RecipeMatch({
    required this.recipe,
    required this.ingredients,
    required this.matchPercentage,
    required this.availableIngredientsCount,
    required this.totalIngredientsCount,
    required this.missingIngredientsCount,
  });

  // Get list of available ingredients
  List<IngredientWithQuantity> get availableIngredients {
    return ingredients.where((ingredient) => ingredient.isAvailable).toList();
  }

  // Get list of missing ingredients (excluding optional ones)
  List<IngredientWithQuantity> get missingRequiredIngredients {
    return ingredients
        .where((ingredient) => !ingredient.isAvailable && !ingredient.isOptional)
        .toList();
  }

  // Get list of missing optional ingredients
  List<IngredientWithQuantity> get missingOptionalIngredients {
    return ingredients
        .where((ingredient) => !ingredient.isAvailable && ingredient.isOptional)
        .toList();
  }

  // Check if recipe can be made (has all required ingredients)
  bool get canBeMade {
    return missingRequiredIngredients.isEmpty;
  }

  // Get match status for UI display
  RecipeMatchStatus get matchStatus {
    if (canBeMade) {
      if (matchPercentage == 100.0) {
        return RecipeMatchStatus.perfect;
      } else {
        return RecipeMatchStatus.good; // Missing only optional ingredients
      }
    } else if (matchPercentage >= 70.0) {
      return RecipeMatchStatus.partial;
    } else {
      return RecipeMatchStatus.poor;
    }
  }

  @override
  String toString() {
    return 'RecipeMatch{recipe: ${recipe.title}, matchPercentage: $matchPercentage%, available: $availableIngredientsCount/$totalIngredientsCount, canBeMade: $canBeMade}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecipeMatch &&
        other.recipe == recipe &&
        other.matchPercentage == matchPercentage &&
        other.availableIngredientsCount == availableIngredientsCount &&
        other.totalIngredientsCount == totalIngredientsCount;
  }

  @override
  int get hashCode {
    return recipe.hashCode ^
        matchPercentage.hashCode ^
        availableIngredientsCount.hashCode ^
        totalIngredientsCount.hashCode;
  }
}

// Enum for recipe match status
enum RecipeMatchStatus {
  perfect('Đầy đủ nguyên liệu', '✅'),
  good('Thiếu nguyên liệu tùy chọn', '✅'),
  partial('Thiếu 1-2 nguyên liệu', '⚠️'),
  poor('Thiếu nhiều nguyên liệu', '❌');

  const RecipeMatchStatus(this.displayName, this.icon);
  final String displayName;
  final String icon;
}

// Class for user's selected ingredients
class UserIngredientSelection {
  final Set<int> selectedIngredientIds;
  final DateTime lastUpdated;

  const UserIngredientSelection({
    required this.selectedIngredientIds,
    required this.lastUpdated,
  });

  // Convert to Map for storage
  Map<String, dynamic> toMap() {
    return {
      'selected_ingredient_ids': selectedIngredientIds.toList(),
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  // Convert from Map
  factory UserIngredientSelection.fromMap(Map<String, dynamic> map) {
    return UserIngredientSelection(
      selectedIngredientIds: Set<int>.from(map['selected_ingredient_ids'] as List),
      lastUpdated: DateTime.parse(map['last_updated'] as String),
    );
  }

  // Create empty selection
  factory UserIngredientSelection.empty() {
    return UserIngredientSelection(
      selectedIngredientIds: {},
      lastUpdated: DateTime.now(),
    );
  }

  // Add ingredient
  UserIngredientSelection addIngredient(int ingredientId) {
    final newSelection = Set<int>.from(selectedIngredientIds);
    newSelection.add(ingredientId);
    return UserIngredientSelection(
      selectedIngredientIds: newSelection,
      lastUpdated: DateTime.now(),
    );
  }

  // Remove ingredient
  UserIngredientSelection removeIngredient(int ingredientId) {
    final newSelection = Set<int>.from(selectedIngredientIds);
    newSelection.remove(ingredientId);
    return UserIngredientSelection(
      selectedIngredientIds: newSelection,
      lastUpdated: DateTime.now(),
    );
  }

  // Toggle ingredient
  UserIngredientSelection toggleIngredient(int ingredientId) {
    if (selectedIngredientIds.contains(ingredientId)) {
      return removeIngredient(ingredientId);
    } else {
      return addIngredient(ingredientId);
    }
  }

  // Check if ingredient is selected
  bool isSelected(int ingredientId) {
    return selectedIngredientIds.contains(ingredientId);
  }

  @override
  String toString() {
    return 'UserIngredientSelection{selectedCount: ${selectedIngredientIds.length}, lastUpdated: $lastUpdated}';
  }
}
